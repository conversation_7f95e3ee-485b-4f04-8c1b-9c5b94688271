"""
Dry Run Handler for Workflow Engine

Handles dry run execution of workflows, showing execution plans and
validating dependencies without actually executing steps.

Author: Assistant
"""

import time
from typing import Optional
from .models import WorkflowConfig, WorkflowResult, StepResult
from .dependency_graph import DependencyGraph


class DryRunHandler:
    """Handles dry run execution of workflows"""

    def __init__(self):
        pass

    async def execute_dry_run(self, config: WorkflowConfig, config_file: Optional[str], start_time: float) -> WorkflowResult:
        """Perform a dry run of the workflow"""

        print(f"🔍 DRY RUN - Workflow: {config.name}")
        if config.description:
            print(f"📝 Description: {config.description}")

        print(f"⚙️  Settings:")
        print(f"   • Max concurrency: {config.settings.max_concurrency}")
        print(f"   • Timeout: {config.settings.timeout}s")
        print(f"   • Fail fast: {config.settings.fail_fast}")

        # Show MCP server information
        if config.mcp_servers:
            print(f"🔌 MCP Servers ({len(config.mcp_servers)}):")
            for server in config.mcp_servers:
                status = "auto-connect" if server.auto_connect else "manual"
                print(f"   • {server.name}: {server.script_path} ({status})")
        else:
            print(f"🔌 MCP Servers: None configured")

        # Validate dependencies and show execution plan
        try:
            dependency_graph = DependencyGraph(config.steps)
            execution_levels = dependency_graph.get_execution_levels()

            print(f"✅ Dependency validation: PASSED")
            print(f"📊 Execution plan ({len(execution_levels)} levels):")

            step_results = []
            steps_by_name = {step.name: step for step in config.steps}

            for level_num, level_steps in enumerate(execution_levels, 1):
                if len(level_steps) == 1:
                    print(f"   Level {level_num}: {level_steps[0]} (sequential)")
                else:
                    print(f"   Level {level_num}: {', '.join(level_steps)} (parallel)")

                for step_name in level_steps:
                    step = steps_by_name[step_name]
                    if step.tool_name:
                        print(f"      • {step_name}: {step.tool_name}")
                    if step.depends_on:
                        print(f"        Dependencies: {', '.join(step.depends_on)}")

                    step_results.append(StepResult(
                        step_name=step.name,
                        step_type=step.type,
                        status="dry_run",
                        execution_time=0.0,
                        data={"would_execute": {
                            "tool_name": step.tool_name,
                            "input_data": step.input_data,
                            "depends_on": step.depends_on,
                            "execution_level": level_num
                        }},
                        tool_name=step.tool_name
                    ))

        except ValueError as e:
            print(f"❌ Dependency validation: FAILED")
            print(f"   Error: {str(e)}")

            # Still create step results for failed validation
            step_results = []
            for step in config.steps:
                step_results.append(StepResult(
                    step_name=step.name,
                    step_type=step.type,
                    status="error",
                    execution_time=0.0,
                    error=f"Dependency validation failed: {str(e)}",
                    tool_name=step.tool_name
                ))
        
        total_time = time.time() - start_time
        
        return WorkflowResult(
            workflow_name=config.name,
            config_file=config_file,
            total_steps=len(config.steps),
            executed_steps=0,
            successful_steps=len(config.steps),
            failed_steps=0,
            skipped_steps=0,
            total_execution_time=round(total_time, 3),
            status="dry_run",
            steps=step_results,
            summary=f"Dry run completed: {len(config.steps)} steps would be executed",
            dry_run=True
        )
