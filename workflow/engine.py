"""
Workflow Execution Engine

The main engine for executing workflows. Handles step orchestration,
dependency resolution, parallel execution, and error handling.

Author: Assistant
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Set, Union
from .models import (
    WorkflowConfig, WorkflowStep, WorkflowResult, StepResult, StepType,
    WorkflowEvent, EventCallback, AsyncEventCallback, MCPServerConfig
)
from .readers import get_reader
from .mcp_adapter import MCPAdapter, MCPServerConfig as AdapterMCPServerConfig
from .dependency_graph import DependencyGraph
from .event_system import EventManager, WorkflowEventFactory
from .step_executor import StepExecutor
from .output_writer import OutputWriter
from .dry_run_handler import DryRunHandler


class WorkflowEngine:
    """
    Dependency graph for workflow steps with topological sorting and cycle detection.

    This class provides efficient dependency resolution using graph algorithms
    instead of the previous O(n²) manual approach.
    """

    def __init__(self, steps: List[WorkflowStep]):
        self.steps_by_name = {step.name: step for step in steps}
        self.graph: Dict[str, Set[str]] = {}  # step -> dependencies
        self.reverse_graph: Dict[str, Set[str]] = {}  # step -> dependents
        self.execution_levels: List[List[str]] = []
        self._build_graphs()
        self._validate_dependencies()
        self._detect_cycles()
        self._calculate_execution_levels()

    def _build_graphs(self) -> None:
        """Build forward and reverse dependency graphs"""
        # Initialize graphs
        for step_name in self.steps_by_name:
            self.graph[step_name] = set()
            self.reverse_graph[step_name] = set()

        # Build dependency relationships
        for step in self.steps_by_name.values():
            self.graph[step.name] = set(step.depends_on)
            for dep in step.depends_on:
                if dep in self.reverse_graph:
                    self.reverse_graph[dep].add(step.name)

    def _validate_dependencies(self) -> None:
        """Validate that all dependencies exist"""
        for step in self.steps_by_name.values():
            for dep in step.depends_on:
                if dep not in self.steps_by_name:
                    raise ValueError(f"Step '{step.name}' depends on non-existent step '{dep}'")

    def _detect_cycles(self) -> None:
        """Detect circular dependencies using DFS"""
        WHITE, GRAY, BLACK = 0, 1, 2
        colors = {step: WHITE for step in self.steps_by_name}

        def dfs(node: str, path: List[str]) -> Optional[List[str]]:
            if colors[node] == GRAY:
                # Found a cycle - return the cycle path
                cycle_start = path.index(node)
                return path[cycle_start:] + [node]

            if colors[node] == BLACK:
                return None

            colors[node] = GRAY
            path.append(node)

            for dependency in self.graph[node]:
                cycle = dfs(dependency, path)
                if cycle:
                    return cycle

            path.pop()
            colors[node] = BLACK
            return None

        for step in self.steps_by_name:
            if colors[step] == WHITE:
                cycle = dfs(step, [])
                if cycle:
                    cycle_str = " -> ".join(cycle)
                    raise ValueError(f"Circular dependency detected: {cycle_str}")

    def _calculate_execution_levels(self) -> None:
        """Calculate execution levels using topological sort (Kahn's algorithm)"""
        # Calculate in-degrees
        in_degree = {step: len(deps) for step, deps in self.graph.items()}

        # Initialize queue with steps that have no dependencies
        queue = deque([step for step, degree in in_degree.items() if degree == 0])

        levels = []

        while queue:
            # All steps in current queue can execute in parallel
            current_level = list(queue)
            levels.append(current_level)

            # Process current level
            next_queue = deque()
            for step in current_level:
                # Remove this step and update in-degrees of dependents
                for dependent in self.reverse_graph[step]:
                    in_degree[dependent] -= 1
                    if in_degree[dependent] == 0:
                        next_queue.append(dependent)

            queue = next_queue

        # Verify all steps were processed (no cycles)
        total_processed = sum(len(level) for level in levels)
        if total_processed != len(self.steps_by_name):
            remaining = [step for step, degree in in_degree.items() if degree > 0]
            raise ValueError(f"Failed to resolve dependencies for steps: {remaining}")

        self.execution_levels = levels

    def get_execution_levels(self) -> List[List[str]]:
        """Get the calculated execution levels"""
        return self.execution_levels

    def get_ready_steps(self, executed_steps: Set[str], failed_steps: Set[str]) -> List[str]:
        """Get steps that are ready to execute given current state"""
        ready = []
        for step_name in self.steps_by_name:
            if (step_name not in executed_steps and
                step_name not in failed_steps and
                all(dep in executed_steps for dep in self.graph[step_name])):
                ready.append(step_name)
        return ready

    def get_step_dependencies(self, step_name: str) -> Set[str]:
        """Get direct dependencies of a step"""
        return self.graph.get(step_name, set())

    def get_step_dependents(self, step_name: str) -> Set[str]:
        """Get direct dependents of a step"""
        return self.reverse_graph.get(step_name, set())

    def get_affected_steps(self, failed_step: str) -> Set[str]:
        """Get all steps that will be affected by a failed step"""
        affected = set()
        queue = deque([failed_step])

        while queue:
            current = queue.popleft()
            for dependent in self.reverse_graph.get(current, set()):
                if dependent not in affected:
                    affected.add(dependent)
                    queue.append(dependent)

        return affected


class WorkflowEngine:
    """Main workflow execution engine with MCP support"""

    def __init__(self, event_callbacks: Optional[List[Union[EventCallback, AsyncEventCallback]]] = None):
        self.tools_module = None
        self._load_tools_module()

        # Event system
        self.event_callbacks = event_callbacks or []

        # MCP adapter for external tool execution
        self.mcp_adapter = MCPAdapter()
    
    def _load_tools_module(self):
        """Load the tools module for executing tool steps"""
        try:
            import tools
            self.tools_module = tools
        except ImportError:
            raise ImportError("Tools module not available. Ensure tools package is installed.")

    async def _setup_mcp_servers(self, mcp_configs: List[MCPServerConfig]) -> None:
        """Setup and connect to MCP servers"""
        for config in mcp_configs:
            if config.auto_connect:
                try:
                    adapter_config = AdapterMCPServerConfig(
                        name=config.name,
                        script_path=config.script_path,
                        env=config.env
                    )
                    await self.mcp_adapter.add_server(adapter_config)
                    print(f"✅ Connected to MCP server: {config.name}")
                except Exception as e:
                    print(f"⚠️  Failed to connect to MCP server {config.name}: {str(e)}")

    async def _cleanup_mcp_servers(self) -> None:
        """Cleanup MCP server connections"""
        await self.mcp_adapter.cleanup()

    async def _fire_event(self, event: WorkflowEvent) -> None:
        """Fire an event to all registered callbacks"""
        for callback in self.event_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                # Log error but don't stop workflow execution
                print(f"⚠️  Event callback error: {e}")

    def _create_workflow_progress(self, config: WorkflowConfig, executed_steps: Set[str],
                                 failed_steps: Set[str], step_results: List[StepResult]) -> Dict[str, Any]:
        """Create workflow progress information"""
        total_steps = len(config.steps)
        completed_steps = len(executed_steps) + len(failed_steps)
        successful_steps = len([r for r in step_results if r.status == "success"])

        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "successful_steps": successful_steps,
            "failed_steps": len(failed_steps),
            "progress_percentage": round((completed_steps / total_steps) * 100, 2) if total_steps > 0 else 0,
            "executed_step_names": list(executed_steps),
            "failed_step_names": list(failed_steps)
        }
    
    async def execute_from_file(self, config_file: str, dry_run: bool = False, 
                               variables: Optional[Dict[str, Any]] = None) -> WorkflowResult:
        """Execute workflow from configuration file"""
        
        # Read and parse configuration
        reader = get_reader(config_file)
        config = reader.read(config_file)
        
        # Override variables if provided
        if variables:
            config.variables.update(variables)
        
        return await self.execute(config, dry_run=dry_run, config_file=config_file)
    
    async def execute(self, config: WorkflowConfig, dry_run: bool = False,
                     config_file: Optional[str] = None) -> WorkflowResult:
        """Execute a workflow configuration"""

        start_time = time.time()

        # Fire workflow started event
        await self._fire_event(WorkflowEvent(
            event_type="workflow_started",
            workflow_name=config.name,
            workflow_progress={"total_steps": len(config.steps), "completed_steps": 0},
            metadata={"config_file": config_file, "dry_run": dry_run}
        ))

        if dry_run:
            return await self._dry_run(config, config_file, start_time)

        # Setup MCP servers if configured
        if config.mcp_servers:
            try:
                await self._setup_mcp_servers(config.mcp_servers)
            except Exception as e:
                print(f"⚠️  MCP server setup failed: {str(e)}")

        # Build dependency graph with validation and cycle detection
        try:
            dependency_graph = DependencyGraph(config.steps)
        except ValueError as e:
            # Return error result for dependency issues
            return WorkflowResult(
                workflow_name=config.name,
                config_file=config_file,
                total_steps=len(config.steps),
                executed_steps=0,
                successful_steps=0,
                failed_steps=len(config.steps),
                skipped_steps=0,
                total_execution_time=round(time.time() - start_time, 3),
                status="failed",
                steps=[StepResult(
                    step_name="dependency_validation",
                    step_type=StepType.TOOL,
                    status="error",
                    execution_time=0.0,
                    error=f"Dependency validation failed: {str(e)}"
                )],
                summary=f"Workflow failed due to dependency issues: {str(e)}",
                dry_run=False
            )

        # Execute steps using dependency graph
        step_results = []
        executed_steps = set()
        failed_steps = set()
        steps_by_name = {step.name: step for step in config.steps}

        try:
            # Execute steps respecting dependencies using graph
            while len(executed_steps) + len(failed_steps) < len(config.steps):
                # Get steps that are ready to execute
                ready_step_names = dependency_graph.get_ready_steps(executed_steps, failed_steps)

                if not ready_step_names:
                    # No more steps can execute - mark remaining as skipped
                    remaining_step_names = [
                        name for name in steps_by_name.keys()
                        if name not in executed_steps and name not in failed_steps
                    ]

                    for step_name in remaining_step_names:
                        step = steps_by_name[step_name]
                        step_results.append(StepResult(
                            step_name=step.name,
                            step_type=step.type,
                            status="skipped",
                            execution_time=0.0,
                            error="Skipped due to failed dependencies",
                            tool_name=step.tool_name
                        ))
                        failed_steps.add(step.name)
                    break

                ready_steps = [steps_by_name[name] for name in ready_step_names]
                
                # Execute ready steps (potentially in parallel)
                if len(ready_steps) == 1 or config.settings.max_concurrency == 1:
                    # Execute sequentially
                    for step in ready_steps:
                        result = await self._execute_step(step, config)
                        step_results.append(result)

                        # Fire step completed event
                        progress = self._create_workflow_progress(config, executed_steps, failed_steps, step_results)
                        await self._fire_event(WorkflowEvent(
                            event_type="step_completed",
                            workflow_name=config.name,
                            step_name=step.name,
                            step_result=result,
                            workflow_progress=progress,
                            metadata={"step_type": step.type.value, "tool_name": step.tool_name}
                        ))

                        if result.status == "success":
                            executed_steps.add(step.name)
                        else:
                            failed_steps.add(step.name)
                            if config.settings.fail_fast:
                                break
                else:
                    # Execute in parallel with concurrency limit
                    semaphore = asyncio.Semaphore(min(config.settings.max_concurrency, len(ready_steps)))
                    
                    async def execute_with_semaphore(step):
                        async with semaphore:
                            return await self._execute_step(step, config)
                    
                    tasks = [execute_with_semaphore(step) for step in ready_steps]
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            result = StepResult(
                                step_name=ready_steps[i].name,
                                step_type=ready_steps[i].type,
                                status="error",
                                execution_time=0.0,
                                error=f"Execution exception: {str(result)}",
                                tool_name=ready_steps[i].tool_name
                            )

                        step_results.append(result)

                        # Fire step completed event for parallel execution
                        progress = self._create_workflow_progress(config, executed_steps, failed_steps, step_results)
                        await self._fire_event(WorkflowEvent(
                            event_type="step_completed",
                            workflow_name=config.name,
                            step_name=result.step_name,
                            step_result=result,
                            workflow_progress=progress,
                            metadata={"step_type": ready_steps[i].type.value, "tool_name": ready_steps[i].tool_name, "parallel_execution": True}
                        ))

                        if result.status == "success":
                            executed_steps.add(result.step_name)
                        else:
                            failed_steps.add(result.step_name)
                
                # Check fail_fast condition
                if config.settings.fail_fast and failed_steps:
                    break
        
        except Exception as e:
            # Handle unexpected errors
            step_results.append(StepResult(
                step_name="workflow_error",
                step_type=StepType.TOOL,
                status="error",
                execution_time=0.0,
                error=f"Workflow execution error: {str(e)}"
            ))
        
        # Calculate final statistics
        total_time = time.time() - start_time
        successful_count = len([r for r in step_results if r.status == "success"])
        failed_count = len([r for r in step_results if r.status == "error"])
        skipped_count = len([r for r in step_results if r.status == "skipped"])
        
        # Determine overall status
        if failed_count == 0:
            overall_status = "success"
        elif successful_count == 0:
            overall_status = "failed"
        else:
            overall_status = "partial"
        
        summary = f"Executed {len(step_results)} steps: {successful_count} successful, {failed_count} failed, {skipped_count} skipped"
        
        result = WorkflowResult(
            workflow_name=config.name,
            config_file=config_file,
            total_steps=len(config.steps),
            executed_steps=len(executed_steps),
            successful_steps=successful_count,
            failed_steps=failed_count,
            skipped_steps=skipped_count,
            total_execution_time=round(total_time, 3),
            status=overall_status,
            steps=step_results,
            summary=summary,
            dry_run=False
        )
        
        # Write output files if configured
        await self._write_output_files(result, config.output)

        # Fire workflow completed event
        await self._fire_event(WorkflowEvent(
            event_type="workflow_completed",
            workflow_name=config.name,
            workflow_progress=self._create_workflow_progress(config, executed_steps, failed_steps, step_results),
            metadata={
                "config_file": config_file,
                "total_execution_time": result.total_execution_time,
                "overall_status": result.status,
                "summary": result.summary
            }
        ))

        # Cleanup MCP servers
        await self._cleanup_mcp_servers()

        return result
    
    async def _dry_run(self, config: WorkflowConfig, config_file: Optional[str], start_time: float) -> WorkflowResult:
        """Perform a dry run of the workflow"""

        print(f"🔍 DRY RUN - Workflow: {config.name}")
        if config.description:
            print(f"📝 Description: {config.description}")

        print(f"⚙️  Settings:")
        print(f"   • Max concurrency: {config.settings.max_concurrency}")
        print(f"   • Timeout: {config.settings.timeout}s")
        print(f"   • Fail fast: {config.settings.fail_fast}")

        # Show MCP server information
        if config.mcp_servers:
            print(f"🔌 MCP Servers ({len(config.mcp_servers)}):")
            for server in config.mcp_servers:
                status = "auto-connect" if server.auto_connect else "manual"
                print(f"   • {server.name}: {server.script_path} ({status})")
        else:
            print(f"🔌 MCP Servers: None configured")

        # Validate dependencies and show execution plan
        try:
            dependency_graph = DependencyGraph(config.steps)
            execution_levels = dependency_graph.get_execution_levels()

            print(f"✅ Dependency validation: PASSED")
            print(f"📊 Execution plan ({len(execution_levels)} levels):")

            step_results = []
            steps_by_name = {step.name: step for step in config.steps}

            for level_num, level_steps in enumerate(execution_levels, 1):
                if len(level_steps) == 1:
                    print(f"   Level {level_num}: {level_steps[0]} (sequential)")
                else:
                    print(f"   Level {level_num}: {', '.join(level_steps)} (parallel)")

                for step_name in level_steps:
                    step = steps_by_name[step_name]
                    if step.tool_name:
                        print(f"      • {step_name}: {step.tool_name}")
                    if step.depends_on:
                        print(f"        Dependencies: {', '.join(step.depends_on)}")

                    step_results.append(StepResult(
                        step_name=step.name,
                        step_type=step.type,
                        status="dry_run",
                        execution_time=0.0,
                        data={"would_execute": {
                            "tool_name": step.tool_name,
                            "input_data": step.input_data,
                            "depends_on": step.depends_on,
                            "execution_level": level_num
                        }},
                        tool_name=step.tool_name
                    ))

        except ValueError as e:
            print(f"❌ Dependency validation: FAILED")
            print(f"   Error: {str(e)}")

            # Still create step results for failed validation
            step_results = []
            for step in config.steps:
                step_results.append(StepResult(
                    step_name=step.name,
                    step_type=step.type,
                    status="error",
                    execution_time=0.0,
                    error=f"Dependency validation failed: {str(e)}",
                    tool_name=step.tool_name
                ))
        
        total_time = time.time() - start_time
        
        return WorkflowResult(
            workflow_name=config.name,
            config_file=config_file,
            total_steps=len(config.steps),
            executed_steps=0,
            successful_steps=len(config.steps),
            failed_steps=0,
            skipped_steps=0,
            total_execution_time=round(total_time, 3),
            status="dry_run",
            steps=step_results,
            summary=f"Dry run completed: {len(config.steps)} steps would be executed",
            dry_run=True
        )
    
    async def _execute_step(self, step: WorkflowStep, config: WorkflowConfig) -> StepResult:
        """Execute a single workflow step"""
        
        start_time = time.time()
        
        try:
            if step.type == StepType.TOOL:
                # Execute tool step
                if not step.tool_name:
                    raise ValueError(f"Tool step '{step.name}' missing tool_name")
                
                # Substitute variables in input data
                input_data = self._substitute_variables(step.input_data, config.variables)

                # Check if this is an MCP tool or local tool
                if self.mcp_adapter.is_mcp_tool(step.tool_name):
                    # Execute MCP tool
                    result = await self.mcp_adapter.execute_tool(step.tool_name, input_data)
                else:
                    # Execute local tool (tools.execute is async-compatible)
                    result = await asyncio.get_event_loop().run_in_executor(
                        None, self.tools_module.execute, step.tool_name, input_data
                    )
                
                return StepResult(
                    step_name=step.name,
                    step_type=step.type,
                    status=result.status,
                    execution_time=round(time.time() - start_time, 3),
                    data=result.data,
                    error=result.error,
                    tool_name=step.tool_name
                )
            
            elif step.type == StepType.PARALLEL:
                # Execute parallel steps
                if not step.parallel_steps:
                    raise ValueError(f"Parallel step '{step.name}' has no parallel_steps defined")
                
                # Execute all parallel steps concurrently
                tasks = [self._execute_step(parallel_step, config) for parallel_step in step.parallel_steps]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Aggregate results
                successful = len([r for r in results if isinstance(r, StepResult) and r.status == "success"])
                total = len(results)
                
                return StepResult(
                    step_name=step.name,
                    step_type=step.type,
                    status="success" if successful == total else "partial",
                    execution_time=round(time.time() - start_time, 3),
                    data={"parallel_results": results, "successful": successful, "total": total},
                    tool_name=None
                )
            
            else:
                # Other step types not yet implemented
                return StepResult(
                    step_name=step.name,
                    step_type=step.type,
                    status="error",
                    execution_time=round(time.time() - start_time, 3),
                    error=f"Step type '{step.type}' not yet implemented",
                    tool_name=step.tool_name
                )
        
        except Exception as e:
            return StepResult(
                step_name=step.name,
                step_type=step.type,
                status="error",
                execution_time=round(time.time() - start_time, 3),
                error=str(e),
                tool_name=step.tool_name
            )
    

    def _substitute_variables(self, data: Any, variables: Dict[str, Any]) -> Any:
        """Substitute variables in data structures"""
        if isinstance(data, str):
            # Simple variable substitution: ${variable_name}
            for var_name, var_value in variables.items():
                data = data.replace(f"${{{var_name}}}", str(var_value))
            return data
        elif isinstance(data, dict):
            return {k: self._substitute_variables(v, variables) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._substitute_variables(item, variables) for item in data]
        else:
            return data
    
    async def _write_output_files(self, result: WorkflowResult, output_config):
        """Write output files if configured"""
        if output_config.summary_file:
            try:
                summary_data = {
                    "workflow_result": result.dict(),
                    "timestamp": time.time()
                }
                
                if output_config.format.lower() == "yaml":
                    import yaml
                    content = yaml.dump(summary_data, default_flow_style=False)
                else:
                    content = json.dumps(summary_data, indent=2)
                
                # Use tools to write the file
                await asyncio.get_event_loop().run_in_executor(
                    None, self.tools_module.execute, "file.write", {
                        "path": output_config.summary_file,
                        "content": content,
                        "overwrite": True
                    }
                )
                
            except Exception as e:
                print(f"⚠️  Failed to write summary file: {e}")
