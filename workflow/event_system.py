"""
Event System for Workflow Engine

Handles event firing and callback management for workflow execution events.

Author: Assistant
"""

import asyncio
from typing import List, Union, Optional, Dict, Any, Set
from .models import WorkflowEvent, EventCallback, AsyncEventCallback, WorkflowConfig, StepResult


class EventManager:
    """Manages event callbacks and firing for workflow events"""

    def __init__(self, event_callbacks: Optional[List[Union[EventCallback, AsyncEventCallback]]] = None):
        self.event_callbacks = event_callbacks or []

    async def fire_event(self, event: WorkflowEvent) -> None:
        """Fire an event to all registered callbacks"""
        for callback in self.event_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                # Log error but don't stop workflow execution
                print(f"⚠️  Event callback error: {e}")

    def add_callback(self, callback: Union[EventCallback, AsyncEventCallback]) -> None:
        """Add an event callback"""
        self.event_callbacks.append(callback)

    def remove_callback(self, callback: Union[EventCallback, AsyncEventCallback]) -> None:
        """Remove an event callback"""
        if callback in self.event_callbacks:
            self.event_callbacks.remove(callback)

    def clear_callbacks(self) -> None:
        """Clear all event callbacks"""
        self.event_callbacks.clear()


class WorkflowEventFactory:
    """Factory for creating workflow events with consistent data"""

    @staticmethod
    def create_workflow_progress(config: WorkflowConfig, executed_steps: Set[str],
                               failed_steps: Set[str], step_results: List[StepResult]) -> Dict[str, Any]:
        """Create workflow progress information"""
        total_steps = len(config.steps)
        completed_steps = len(executed_steps) + len(failed_steps)
        successful_steps = len([r for r in step_results if r.status == "success"])

        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "successful_steps": successful_steps,
            "failed_steps": len(failed_steps),
            "progress_percentage": round((completed_steps / total_steps) * 100, 2) if total_steps > 0 else 0,
            "executed_step_names": list(executed_steps),
            "failed_step_names": list(failed_steps)
        }

    @staticmethod
    def workflow_started(config: WorkflowConfig, config_file: Optional[str] = None, 
                        dry_run: bool = False) -> WorkflowEvent:
        """Create a workflow started event"""
        return WorkflowEvent(
            event_type="workflow_started",
            workflow_name=config.name,
            workflow_progress={"total_steps": len(config.steps), "completed_steps": 0},
            metadata={"config_file": config_file, "dry_run": dry_run}
        )

    @staticmethod
    def step_completed(config: WorkflowConfig, step_name: str, step_result: StepResult,
                      executed_steps: Set[str], failed_steps: Set[str], 
                      step_results: List[StepResult], parallel_execution: bool = False) -> WorkflowEvent:
        """Create a step completed event"""
        progress = WorkflowEventFactory.create_workflow_progress(config, executed_steps, failed_steps, step_results)
        
        # Find the step to get its details
        step = next((s for s in config.steps if s.name == step_name), None)
        
        return WorkflowEvent(
            event_type="step_completed",
            workflow_name=config.name,
            step_name=step_name,
            step_result=step_result,
            workflow_progress=progress,
            metadata={
                "step_type": step.type.value if step else "unknown",
                "tool_name": step_result.tool_name,
                "parallel_execution": parallel_execution
            }
        )

    @staticmethod
    def workflow_completed(config: WorkflowConfig, executed_steps: Set[str], failed_steps: Set[str],
                          step_results: List[StepResult], total_execution_time: float,
                          overall_status: str, summary: str, config_file: Optional[str] = None) -> WorkflowEvent:
        """Create a workflow completed event"""
        progress = WorkflowEventFactory.create_workflow_progress(config, executed_steps, failed_steps, step_results)
        
        return WorkflowEvent(
            event_type="workflow_completed",
            workflow_name=config.name,
            workflow_progress=progress,
            metadata={
                "config_file": config_file,
                "total_execution_time": total_execution_time,
                "overall_status": overall_status,
                "summary": summary
            }
        )
