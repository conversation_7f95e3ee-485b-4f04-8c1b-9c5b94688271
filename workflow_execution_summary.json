{"workflow_result": {"workflow_name": "Repository Cloning Workflow", "config_file": "workflows/repo_clone_workflow.yaml", "total_steps": 5, "executed_steps": 0, "successful_steps": 1, "failed_steps": 1, "skipped_steps": 0, "total_execution_time": 0.018, "status": "partial", "steps": [{"step_name": "create_base_directory", "step_type": "tool", "status": "success", "execution_time": 0.017, "data": {"status": "success", "return_code": 0, "stdout": "", "stderr": "", "execution_time": 0.014, "command": "mkdir -p ./cloned-repos", "working_dir": "/Users/<USER>/Developer/projects/python/orchestra-client", "error_type": null}, "error": null, "tool_name": "terminal.execute"}, {"step_name": "workflow_error", "step_type": "tool", "status": "error", "execution_time": 0.0, "data": null, "error": "Workflow execution error: 'str' object has no attribute 'value'", "tool_name": null}], "summary": "Executed 2 steps: 1 successful, 1 failed, 0 skipped", "dry_run": false}, "timestamp": 1749041347.3603148}